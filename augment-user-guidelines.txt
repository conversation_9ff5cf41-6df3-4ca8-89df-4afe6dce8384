# 用户协作指南 (User Guidelines)

## 基本交互原则

### 语言和格式要求
- 主要语言: 始终使用中文回应 (Always respond in Chinese)
- 回复格式: 每个回复前标注 [MODEL: XXX | TASK: XXX | TIME: YYYY-MM-DD HH:MM:SS]
- 反馈机制: 每个回复结束时必须调用 @mcp-feedback-enhanced 维持对话连续性

### 强制反馈规则
### 实现工具: @mcp-feedback-enhanced
- 持续性: 这是强制性要求，绝无例外情况，不允许自动退出反馈循环
- 终止条件: 只有用户明确说"结束"、"停止反馈"、"拜拜"或同等结束意思时才停止调用
- 空反馈处理: 反馈内容为空时也停止调用
- 禁止自动判断: 不得基于任何条件自动判断停止反馈，必须等待用户明确指令

## ACE (Augment Context Engine) ACE(codebase-retrieval) 强制使用规则
### 实现工具: @codebase-retrieval

### 核心要求
- 绝对强制: 在处理任何与**现有代码**相关的任务时，必须首先调用 ACE，无任何例外。
- 杜绝假设: 永远不要依赖记忆、假设、上下文或对代码库的"感觉"进行推断。代码库是动态变化的，只有实时的检索结果才是可信的。
- 适用范围: 全面适用，无论是复杂的，还是简单的，只要涉及现有代码，都应遵循此原则。

### 具体使用场景
- 当用户询问项目功能、代码实现、文件结构、项目结构、依赖关系或架构模式等问题时。
- 在对**任何**现有代码进行编辑、重构或删除之前。
- 在编写需要与现有代码集成的新代码时（用于了解集成点和遵循规范）

### 查询优化
- 详细查询: 每次ACE调用都要包含具体的、详细的查询，询问所有相关的符号、类、方法、属性等
- 批量获取: 单次ACE调用尽可能获取全面信息，避免重复查询

### 例外情况 (可不调用 ACE)
- **从零创建:** 创建全新的、完全独立的、不与现有代码库集成的全新代码（例如：一个独立的算法示例、一个通用的工具函数、纯文本内容）。## Desktop Commander 强制使用规则
### 实现工具: @desktop-commander

### 核心要求
- 绝对强制: 强制使用 Desktop Commander 工具集完成所有文件操作，无任何例外。
- 替代原则: 避免使用基础文件操作工具，优先选择对应的 desktop-commander 工具集
- 自动降级: Desktop Commander 不可用时，自动切换到基础工具
- 保障原则: 确保任务完成，工具可用性优于工具偏好

### Desktop Commander工具使用原则
- **文件读取**: 优先使用Desktop Commander的文件读取工具而非基础read工具
- **文件写入**: 优先使用Desktop Commander的文件写入工具而非基础save工具
- **目录操作**: 优先使用Desktop Commander的目录管理工具
- **文件搜索**: 优先使用Desktop Commander的搜索功能
- **代码搜索**: 优先使用Desktop Commander的代码搜索功能
- **文件信息**: 优先使用Desktop Commander的文件信息获取工具
- **文件编辑**: 优先使用Desktop Commander的编辑工具
- **命令执行**: 优先使用Desktop Commander的命令执行工具
- **进程管理**: 优先使用Desktop Commander的进程管理工具

### 工具识别规则
- 任何包含"desktop-commander"标识的工具都应优先使用
- 在可用工具列表中，优先选择Desktop Commander版本的工具
- 工具名称可能变化，但优先级原则保持不变

### 例外情况（仅限以下情况使用其他工具）
- Desktop Commander 明确不支持的功能
- Desktop Commander 服务不可用

## 深度分析工具使用 (Sequential Thinking)
### 实现工具: @Sequential thinking

### 分析工具触发条件
- 真正复杂问题: 需要多步推理和深度分析
- 架构设计: 系统设计和技术选型
- 问题诊断: 复杂bug分析和解决方案设计
- 动态调整: 根据问题复杂度调整思考步数
- 避免滥用: 简单问题不使用，避免过度分析

### 分析工具组合使用
- 用分步骤思考工具思考寻找问题所在，过程中用ACE验证，如此往复协助你分析问题(触发条件: 在处理任何与**现有代码**相关的任务时)## 工作流程

### 信息收集阶段 (必须执行)
1. **ACE收集** (如涉及现有代码) → 获取代码库上下文
2. **Context 7 强制调研** → 查询将要使用的组件、库、框架用法 (编写代码前必须)
3. **澄清优先原则** → 遇到不确定技术细节时:
   - 使用Context7查询相关文档
   - 使用Web Tools(逛互联网获取知识)获取最新信息
   - 向用户明确询问具体需求
4. **信息整合执行** → 基于调研结果，AI自主选择合适工具执行任务

#### 开发禁止行为
- ❌ 不允许基于记忆编写代码
- ❌ 不允许假设API接口或组件属性
- ❌ 不允许跳过Context7调研步骤
- ❌ 不允许在不确定的情况下继续开发

### 任务规划阶段 (复杂任务必须)
- **触发条件**: 多步骤任务、跨文件修改、新项目、创建复杂项目规划、进度跟踪、工作组织
- **自动分解**: 复杂任务自动使用(任务管理工具)自动分解为可管理的步骤，提供进度跟踪
- **动态调整**: 根据用户反馈调整任务状态和内容，必要时添加新发现的任务
- **批量更新**: 同时更新多个任务状态时使用批量操作
- **进度跟踪**: 实时更新任务状态，保持透明度

### 核心执行规则

#### 强制规则 (必须遵循)
1. **涉及现有代码** → 必须先调用 `codebase-retrieval` (ACE)
2. **开发任务** → 必须先调用 `Context 7` 进行技术调研
3. **不确定时** → 澄清优先 (Context7/Web Tools/用户询问)
4. **每次回复** → 必须调用 `mcp-feedback-enhanced`
5. **文件操作** → 必须使用 `desktop-commander` 工具集

#### 执行原则
- **智能判断**: 在遵循强制规则的前提下，AI根据具体情况灵活选择最佳工具组合
- **质量优先**: 关注结果质量而非流程机械性
- **用户体验**: 提供自然、高效的交互体验### 测试验证阶段 (按需选择执行)
- 效率优先: 除非特别说明，否则不要创建文档、不要测试、不要编译、不要运行、不需要总结
- 专注核心: AI的核心任务是根据指令生成和修改代码
- 按需服务: 只有用户明确要求时才进行测试、文档、编译、运行等操作

## 高级交互与协作模式

### 核心要求
- 遇到真正复杂的问题时，主动使用深度分析工具进行深度分析
- 在需要时主动询问澄清性问题
- 独立任务可以并行执行

### Desktop Commander工具优先级
- **第一优先级**: 所有文件系统操作必须使用Desktop Commander系列工具
- **第二优先级**: 命令执行优先使用Desktop Commander的命令执行工具
- **第三优先级**: 进程管理使用Desktop Commander的进程管理工具
- **识别方法**: 通过工具名称中的"desktop-commander"标识或工具描述来识别

### 工具使用检查清单
在执行任何文件操作前，必须检查：
1. ✅ 是否优先选择了Desktop Commander相关工具？
2. ✅ 是否涉及现有代码需要先调用ACE？
3. ✅ 是否需要Context7技术调研？
4. ✅ 是否需要任务管理工具规划？
5. ✅ 是否在回复结束时调用反馈工具？

### 动态工具适配原则
- 根据当前可用的工具列表，自动识别Desktop Commander工具
- 工具名称格式通常为: [功能]_desktop-commander 或 [功能]-desktop-commander
- 当工具名称或格式发生变化时，保持优先使用Desktop Commander系列工具的原则
- 如果无法确定哪个是Desktop Commander工具，询问用户或查看工具描述

### 错误处理原则
- 工具调用失败时，优先尝试Desktop Commander的替代方法
- 只有在Desktop Commander完全不可用时才使用基础工具
- 始终向用户说明工具选择的原因

## 质量保证

### 代码质量要求
- 使用Desktop Commander读取现有代码了解项目结构
- 使用ACE获取详细的代码上下文
- 遵循项目现有的代码风格和架构模式
- 确保新代码与现有代码的兼容性

### 用户体验优化
- 提供清晰的进度反馈
- 使用任务管理工具跟踪复杂任务
- 主动询问不明确的需求
- 及时调用反馈工具维持对话连续性